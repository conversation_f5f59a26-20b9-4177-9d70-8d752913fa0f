#!/usr/bin/env python
"""
Basic test for NLP functionality without database dependencies
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CourseRec.settings')
django.setup()

from course_matcher.nlp_service import GeminiNLPService


def test_nlp_service_basic():
    """Test basic NLP service functionality"""
    print("🧠 Testing NLP Service Basic Functionality")
    print("=" * 50)
    
    # Check API key
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found")
        return False
    
    print(f"✓ API key found: {api_key[:10]}...")
    
    try:
        # Initialize service
        print("🔧 Initializing NLP service...")
        nlp_service = GeminiNLPService()
        print("✓ NLP service initialized successfully")
        
        # Test semantic similarity
        print("\n📊 Testing semantic similarity analysis...")
        similarity = nlp_service.analyze_semantic_similarity(
            text1="machine learning and artificial intelligence",
            text2="AI algorithms and neural networks",
            context="academic course matching"
        )
        
        print(f"✓ Similarity analysis completed")
        print(f"  Score: {similarity.score:.2f}")
        print(f"  Explanation: {similarity.explanation}")
        print(f"  Key matches: {similarity.key_matches}")
        
        # Test course content analysis
        print("\n📚 Testing course content analysis...")
        analysis = nlp_service.analyze_course_content(
            course_description="Introduction to machine learning algorithms, supervised and unsupervised learning, neural networks, and practical applications in data science.",
            course_name="Machine Learning Fundamentals",
            course_topics=["Machine Learning", "Neural Networks", "Data Science"]
        )
        
        print(f"✓ Content analysis completed")
        print(f"  Difficulty: {analysis.difficulty_assessment}")
        print(f"  Topics: {analysis.topics[:3]}")
        print(f"  Learning outcomes: {len(analysis.learning_outcomes)} identified")
        
        # Test recommendation explanation
        print("\n💬 Testing recommendation explanation...")
        explanation = nlp_service.generate_recommendation_explanation(
            student_interests=["Machine Learning", "Data Science"],
            student_goals="I want to become a data scientist",
            course_name="Machine Learning Fundamentals",
            course_description="Introduction to ML algorithms and applications",
            similarity_score=0.85,
            traditional_reasoning="This course matches your academic level and interests."
        )
        
        print(f"✓ Explanation generated")
        print(f"  Enhanced explanation: {explanation[:100]}...")
        
        # Test enhanced similarity computation
        print("\n🔍 Testing enhanced similarity computation...")
        enhanced_score = nlp_service.compute_enhanced_similarity(
            student_interests=["Machine Learning", "Data Science"],
            student_goals="I want to become a data scientist specializing in AI",
            course_description="Advanced machine learning course covering deep learning and neural networks",
            course_topics=["Machine Learning", "Deep Learning", "Neural Networks"]
        )
        
        print(f"✓ Enhanced similarity computed: {enhanced_score:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_recommendation_engine_basic():
    """Test recommendation engine without database operations"""
    print("\n🎯 Testing Enhanced Recommendation Engine (Basic)")
    print("=" * 50)
    
    try:
        from course_matcher.recommendation_service import RecommendationEngine
        
        print("🔧 Initializing recommendation engine...")
        engine = RecommendationEngine()
        print("✓ Recommendation engine initialized")
        
        if engine.nlp_service:
            print("✓ NLP service is available in engine")
        else:
            print("⚠️  NLP service not available (fallback mode)")
        
        # Test safe JSON field method
        print("\n🛡️  Testing JSON field safety...")
        test_cases = [
            ('["item1", "item2"]', ["item1", "item2"]),
            ('invalid json', []),
            (["direct", "list"], ["direct", "list"]),
            (None, []),
            ("", [])
        ]
        
        for test_input, expected in test_cases:
            result = engine._safe_get_json_field(test_input, [])
            status = "✓" if result == expected else "❌"
            print(f"  {status} Input: {test_input} -> Output: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_caching():
    """Test caching functionality"""
    print("\n⚡ Testing Caching Performance")
    print("=" * 50)
    
    try:
        import time
        from django.core.cache import cache
        
        # Clear cache
        cache.clear()
        print("🧹 Cache cleared")
        
        nlp_service = GeminiNLPService()
        
        # First call (should hit API)
        print("🔄 Making first API call...")
        start_time = time.time()
        similarity1 = nlp_service.analyze_semantic_similarity(
            "test text one", "test text two", "caching test"
        )
        first_call_time = time.time() - start_time
        print(f"✓ First call completed in {first_call_time:.2f}s")
        
        # Second call (should use cache)
        print("🔄 Making second API call (should use cache)...")
        start_time = time.time()
        similarity2 = nlp_service.analyze_semantic_similarity(
            "test text one", "test text two", "caching test"
        )
        second_call_time = time.time() - start_time
        print(f"✓ Second call completed in {second_call_time:.2f}s")
        
        # Verify results are the same
        if similarity1.score == similarity2.score:
            print("✓ Cache working correctly - same results")
        else:
            print("⚠️  Cache may not be working - different results")
        
        # Calculate speedup
        if second_call_time > 0:
            speedup = first_call_time / second_call_time
            print(f"🚀 Cache speedup: {speedup:.1f}x faster")
        
        return True
        
    except Exception as e:
        print(f"❌ Caching test error: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 CourseRec NLP Enhancement - Basic Tests")
    print("=" * 60)
    
    results = []
    
    # Test NLP service
    results.append(("NLP Service", test_nlp_service_basic()))
    
    # Test recommendation engine
    results.append(("Recommendation Engine", test_recommendation_engine_basic()))
    
    # Test caching
    results.append(("Caching", test_caching()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! NLP enhancement is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    print(f"\n📖 For complete documentation, see:")
    print("   📄 NLP_ENHANCEMENT_DOCUMENTATION.md")


if __name__ == "__main__":
    main()
