"""
Gemini NLP Service for Course Recommendation Enhancement
Provides semantic similarity analysis and natural language processing capabilities
"""
import os
import json
import time
import logging
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
import google.generativeai as genai

# Try to import safety settings, fallback if not available
try:
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    SAFETY_SETTINGS_AVAILABLE = True
except ImportError:
    # Fallback for older versions
    SAFETY_SETTINGS_AVAILABLE = False
    HarmCategory = None
    HarmBlockThreshold = None

logger = logging.getLogger(__name__)


@dataclass
class SemanticSimilarity:
    """Data class for semantic similarity results"""
    score: float
    explanation: str
    key_matches: List[str]


@dataclass
class ContentAnalysis:
    """Data class for content analysis results"""
    topics: List[str]
    difficulty_assessment: str
    learning_outcomes: List[str]
    prerequisites_suggested: List[str]


class GeminiNLPService:
    """
    Service class for Gemini API integration with comprehensive NLP capabilities
    """
    
    def __init__(self):
        self.api_key = os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable not set")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)

        # Check if this is the newer or older version of the library
        self.use_legacy_api = not hasattr(genai, 'GenerativeModel')

        if not self.use_legacy_api:
            # Newer version with GenerativeModel
            model_name = "gemini-pro"

            if SAFETY_SETTINGS_AVAILABLE:
                try:
                    self.model = genai.GenerativeModel(
                        model_name=model_name,
                        safety_settings={
                            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                        }
                    )
                except Exception:
                    self.model = genai.GenerativeModel(model_name=model_name)
            else:
                self.model = genai.GenerativeModel(model_name=model_name)
        else:
            # Older version with generate_text
            self.model = None  # Not needed for legacy API
        
        # Rate limiting configuration
        self.rate_limit_delay = 1.0  # seconds between requests
        self.last_request_time = 0
        self.max_retries = 3
        self.retry_delay = 2.0
        
        # Cache configuration
        self.cache_timeout = 3600 * 24  # 24 hours for course analysis
        self.student_cache_timeout = 3600 * 6  # 6 hours for student profiles
    
    def _rate_limit(self):
        """Implement rate limiting to avoid API quota issues"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, prompt: str, max_tokens: int = 1000) -> Optional[str]:
        """
        Make a request to Gemini API with error handling and retries
        """
        for attempt in range(self.max_retries):
            try:
                self._rate_limit()

                if self.use_legacy_api:
                    # Use legacy generate_text API
                    response = genai.generate_text(
                        prompt=prompt,
                        temperature=0.3,
                        max_output_tokens=max_tokens
                    )

                    if response.result:
                        return response.result.strip()
                    else:
                        logger.warning(f"Empty response from Gemini API on attempt {attempt + 1}")
                else:
                    # Use newer GenerativeModel API
                    response = self.model.generate_content(
                        prompt,
                        generation_config=genai.types.GenerationConfig(
                            max_output_tokens=max_tokens,
                            temperature=0.3,
                        )
                    )

                    if response.text:
                        return response.text.strip()
                    else:
                        logger.warning(f"Empty response from Gemini API on attempt {attempt + 1}")

            except Exception as e:
                logger.error(f"Gemini API error on attempt {attempt + 1}: {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                else:
                    logger.error("Max retries exceeded for Gemini API request")
                    return None

        return None
    
    def analyze_semantic_similarity(self, text1: str, text2: str, context: str = "") -> SemanticSimilarity:
        """
        Analyze semantic similarity between two texts using Gemini
        
        Args:
            text1: First text to compare
            text2: Second text to compare
            context: Additional context for the comparison
            
        Returns:
            SemanticSimilarity object with score and explanation
        """
        cache_key = f"semantic_sim_{hash(text1 + text2 + context)}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return SemanticSimilarity(**cached_result)
        
        prompt = f"""
        Analyze the semantic similarity between these two texts in the context of academic course recommendations.
        
        Text 1: {text1}
        Text 2: {text2}
        Context: {context}
        
        Provide your analysis in the following JSON format:
        {{
            "score": <float between 0.0 and 1.0>,
            "explanation": "<brief explanation of the similarity>",
            "key_matches": ["<key concept 1>", "<key concept 2>", ...]
        }}
        
        Consider:
        - Conceptual overlap and shared themes
        - Academic relevance and learning objectives
        - Skill development alignment
        - Career preparation aspects
        
        Be precise with the similarity score where 0.0 means no similarity and 1.0 means identical meaning.
        """
        
        response = self._make_request(prompt)
        if not response:
            # Fallback to basic similarity
            return SemanticSimilarity(score=0.3, explanation="Unable to analyze similarity", key_matches=[])
        
        try:
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_str = response[json_start:json_end]
                result_data = json.loads(json_str)
                
                # Validate and create result
                similarity = SemanticSimilarity(
                    score=max(0.0, min(1.0, float(result_data.get('score', 0.3)))),
                    explanation=result_data.get('explanation', 'Semantic analysis completed'),
                    key_matches=result_data.get('key_matches', [])
                )
                
                # Cache the result
                cache.set(cache_key, {
                    'score': similarity.score,
                    'explanation': similarity.explanation,
                    'key_matches': similarity.key_matches
                }, self.cache_timeout)
                
                return similarity
                
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"Error parsing Gemini response: {e}")
        
        # Fallback
        return SemanticSimilarity(score=0.3, explanation="Analysis completed with limited data", key_matches=[])
    
    def analyze_course_content(self, course_description: str, course_name: str, course_topics: List[str]) -> ContentAnalysis:
        """
        Perform comprehensive content analysis of a course
        
        Args:
            course_description: Full course description
            course_name: Course name/title
            course_topics: Existing course topics
            
        Returns:
            ContentAnalysis object with extracted information
        """
        cache_key = f"course_analysis_{hash(course_description + course_name)}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return ContentAnalysis(**cached_result)
        
        prompt = f"""
        Analyze this academic course for recommendation purposes:
        
        Course Name: {course_name}
        Description: {course_description}
        Current Topics: {', '.join(course_topics) if course_topics else 'None listed'}
        
        Provide analysis in JSON format:
        {{
            "topics": ["<topic 1>", "<topic 2>", ...],
            "difficulty_assessment": "<beginner|intermediate|advanced>",
            "learning_outcomes": ["<outcome 1>", "<outcome 2>", ...],
            "prerequisites_suggested": ["<prerequisite 1>", "<prerequisite 2>", ...]
        }}
        
        Focus on:
        - Key academic topics and concepts covered
        - Appropriate difficulty level based on content complexity
        - Expected learning outcomes and skills gained
        - Logical prerequisites or foundational knowledge needed
        
        Keep topics concise and relevant for course matching.
        """
        
        response = self._make_request(prompt, max_tokens=800)
        if not response:
            return ContentAnalysis(
                topics=course_topics or [],
                difficulty_assessment="intermediate",
                learning_outcomes=[],
                prerequisites_suggested=[]
            )
        
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_str = response[json_start:json_end]
                result_data = json.loads(json_str)
                
                analysis = ContentAnalysis(
                    topics=result_data.get('topics', course_topics or []),
                    difficulty_assessment=result_data.get('difficulty_assessment', 'intermediate'),
                    learning_outcomes=result_data.get('learning_outcomes', []),
                    prerequisites_suggested=result_data.get('prerequisites_suggested', [])
                )
                
                # Cache the result
                cache.set(cache_key, {
                    'topics': analysis.topics,
                    'difficulty_assessment': analysis.difficulty_assessment,
                    'learning_outcomes': analysis.learning_outcomes,
                    'prerequisites_suggested': analysis.prerequisites_suggested
                }, self.cache_timeout)
                
                return analysis
                
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Error parsing course analysis response: {e}")
        
        # Fallback
        return ContentAnalysis(
            topics=course_topics or [],
            difficulty_assessment="intermediate",
            learning_outcomes=[],
            prerequisites_suggested=[]
        )

    def generate_recommendation_explanation(self, student_interests: List[str], student_goals: str,
                                          course_name: str, course_description: str,
                                          similarity_score: float, traditional_reasoning: str) -> str:
        """
        Generate enhanced natural language explanation for course recommendation

        Args:
            student_interests: List of student's academic interests
            student_goals: Student's career goals
            course_name: Name of the recommended course
            course_description: Course description
            similarity_score: NLP-computed similarity score
            traditional_reasoning: Existing reasoning from traditional algorithm

        Returns:
            Enhanced explanation string
        """
        cache_key = f"explanation_{hash(str(student_interests) + student_goals + course_name)}"
        cached_result = cache.get(cache_key)

        if cached_result:
            return cached_result

        prompt = f"""
        Generate a personalized course recommendation explanation for a student.

        Student Profile:
        - Interests: {', '.join(student_interests) if student_interests else 'Not specified'}
        - Career Goals: {student_goals or 'Not specified'}

        Recommended Course:
        - Name: {course_name}
        - Description: {course_description}

        Analysis Results:
        - Semantic Similarity Score: {similarity_score:.2f}
        - Traditional Analysis: {traditional_reasoning}

        Create a compelling, personalized explanation (2-3 sentences) that:
        1. Connects the course to the student's specific interests and goals
        2. Highlights the most relevant aspects of the course
        3. Uses encouraging, supportive language
        4. Integrates insights from both semantic analysis and traditional factors

        Make it sound natural and personalized, not generic.
        """

        response = self._make_request(prompt, max_tokens=300)
        if not response:
            # Enhanced fallback that combines traditional reasoning with NLP insights
            enhanced_explanation = traditional_reasoning
            if similarity_score > 0.7:
                enhanced_explanation += " This course shows strong alignment with your academic interests and career aspirations."
            elif similarity_score > 0.5:
                enhanced_explanation += " This course offers relevant content that connects to your stated interests."

            return enhanced_explanation

        # Cache and return the generated explanation
        cache.set(cache_key, response, self.student_cache_timeout)
        return response

    def analyze_student_profile(self, interests: List[str], career_goals: str,
                              completed_courses: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Analyze student profile to extract learning patterns and preferences

        Args:
            interests: List of student interests
            career_goals: Student's career goals text
            completed_courses: List of completed courses with descriptions

        Returns:
            Dictionary with analysis results
        """
        cache_key = f"student_profile_{hash(str(interests) + career_goals + str(completed_courses))}"
        cached_result = cache.get(cache_key)

        if cached_result:
            return cached_result

        course_descriptions = [course.get('description', '') for course in completed_courses]

        prompt = f"""
        Analyze this student's academic profile to understand their learning patterns:

        Stated Interests: {', '.join(interests) if interests else 'None specified'}
        Career Goals: {career_goals or 'Not specified'}
        Completed Courses: {'; '.join([f"{c.get('name', '')}: {c.get('description', '')[:100]}..." for c in completed_courses[:5]])}

        Provide analysis in JSON format:
        {{
            "learning_themes": ["<theme 1>", "<theme 2>", ...],
            "skill_progression": "<description of skill development pattern>",
            "recommended_focus_areas": ["<area 1>", "<area 2>", ...],
            "learning_style_indicators": ["<indicator 1>", "<indicator 2>", ...],
            "career_alignment_score": <float 0.0-1.0>
        }}

        Focus on identifying:
        - Common themes across interests and completed courses
        - Evidence of skill progression and academic growth
        - Areas that would benefit the student's career goals
        - Learning preferences based on course choices
        """

        response = self._make_request(prompt, max_tokens=600)
        if not response:
            return {
                'learning_themes': interests or [],
                'skill_progression': 'Steady academic progress',
                'recommended_focus_areas': interests or [],
                'learning_style_indicators': [],
                'career_alignment_score': 0.5
            }

        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_str = response[json_start:json_end]
                result_data = json.loads(json_str)

                # Cache and return the analysis
                cache.set(cache_key, result_data, self.student_cache_timeout)
                return result_data

        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Error parsing student profile analysis: {e}")

        # Fallback
        return {
            'learning_themes': interests or [],
            'skill_progression': 'Steady academic progress',
            'recommended_focus_areas': interests or [],
            'learning_style_indicators': [],
            'career_alignment_score': 0.5
        }

    def compute_enhanced_similarity(self, student_interests: List[str], student_goals: str,
                                  course_description: str, course_topics: List[str]) -> float:
        """
        Compute enhanced similarity score combining multiple NLP analyses

        Args:
            student_interests: Student's interests
            student_goals: Student's career goals
            course_description: Course description
            course_topics: Course topics

        Returns:
            Enhanced similarity score (0.0 to 1.0)
        """
        if not student_interests and not student_goals:
            return 0.3  # Default score when no student data

        scores = []

        # Analyze interests vs course description
        if student_interests and course_description:
            interests_text = ', '.join(student_interests)
            similarity = self.analyze_semantic_similarity(
                interests_text,
                course_description,
                "academic interests and course content"
            )
            scores.append(similarity.score)

        # Analyze career goals vs course description
        if student_goals and course_description:
            similarity = self.analyze_semantic_similarity(
                student_goals,
                course_description,
                "career goals and course learning outcomes"
            )
            scores.append(similarity.score)

        # Analyze interests vs course topics
        if student_interests and course_topics:
            interests_text = ', '.join(student_interests)
            topics_text = ', '.join(course_topics)
            similarity = self.analyze_semantic_similarity(
                interests_text,
                topics_text,
                "academic interests and course topics"
            )
            scores.append(similarity.score)

        # Return weighted average of available scores
        if scores:
            return sum(scores) / len(scores)
        else:
            return 0.3  # Default when no comparisons possible
